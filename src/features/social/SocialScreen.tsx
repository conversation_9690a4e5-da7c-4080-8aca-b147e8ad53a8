import React, { useEffect } from 'react';

interface SocialScreenProps {
  tabId: string;
}

export const SocialScreen: React.FC<SocialScreenProps> = ({ }) => {
  useEffect(() => {
    // Force open in default browser using intent links
    const url = 'https://app.emergent.sh';

    // Detect platform and use appropriate intent
    const userAgent = navigator.userAgent.toLowerCase();

    if (userAgent.includes('android')) {
      // Android intent to force external browser
      const intentUrl = `intent://${url.replace('https://', '')}#Intent;scheme=https;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;end`;
      window.location.href = intentUrl;
    } else if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
      // iOS - try to force Sa<PERSON> by using a different approach
      window.open(url, '_system');
    } else {
      // Desktop/other platforms - use standard approach with intent-like behavior
      try {
        const link = document.createElement('a');
        link.href = url;
        link.target = '_blank';
        link.rel = 'noopener noreferrer external';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        console.error('Failed to open external browser:', error);
        window.location.href = url;
      }
    }
  }, []);

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Social</h1>
        <p className="text-muted-foreground">
          A new window has been opened. You can close this tab.
        </p>
      </div>
    </div>
  );
};
